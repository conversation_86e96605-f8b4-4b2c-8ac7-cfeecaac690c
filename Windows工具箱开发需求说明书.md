
### 1.1 严格遵循原则
- **管理员权限必须**：所有系统级操作必须以管理员权限执行。
- **异步操作优先**：避免阻塞UI线程，所有耗时操作需异步执行并提供进度反馈。
- **Fluent组件优先**：优先使用 PySide6-Fluent-Widgets 组件，若无对应组件，才使用 PySide6 组件。
- **需求严格遵循**：除快捷工具需要分类外，其他视图不允许有刷新、分类、收藏、搜索、回滚、日志等额外功能。
- **界面设计**：所有界面必须严格遵循 Fluent Design 2.0 设计原则和布局指南，禁止使用其他设计风格。

## 2. 框架和组件研究

- 阅读 PySide6 和 PySide6-Fluent-Widgets 官方文档，熟悉所有可用组件和API。
- 界面布局采用左侧导航栏 + 右侧内容区域。
- 设置启动页面和主窗口图标。
- 设置软件任务栏图标

## 3. 启动页面与主窗口

- 使用 SplashScreen 组件实现标准启动页面，无需自定义标题栏和图标。
- 启动页面尺寸与主窗口保持一致。
- 启动页面实现使用context7 MCP工具参考官方示例编写。

## 4. 硬件信息视图

- 使用 Fluent-Widgets 组件展示硬件信息。
- 硬件信息顺序及复制顺序严格按照以下内容：

  ### 系统信息
  - 操作系统: Microsoft Windows 11 专业工作站版 Insider Preview
  - 系统版本: 10.0.26200
  - 系统架构: 64 位
  - 计算机名: DESKTOP-587MD10

  ### 主板信息
  - 主板制造商: Micro-Star International Co., Ltd.
  - 主板型号: Z790MPOWER (MS-7E01)
  - BIOS制造商: American Megatrends International, LLC.
  - BIOS日期: 2024-04-10T00:00:00
  - BIOS版本: P.21U4

  ### 处理器信息
  - 制造商: GenuineIntel
  - 型号: Intel(R) Core(TM) i7-14700KF
  - 当前频率: 5700 MHz
  - 最大频率: 5700 MHz

  ### 内存信息
  - 内存条 xx: M-POWER8036AW16  8000 MHz  （16.00GB）
  - 内存条 xx: M-POWER8036AW16  8000 MHz  （16.00GB）
  - 总容量: 32GB （16GBx2），需特殊处理不同容量内存条情况。

  ### 显卡信息
  - 显卡名称: NVIDIA GeForce RTX 4070 SUPER
  - 驱动版本: 32.0.15.6094
  - 分辨率: 3840x2160@160Hz

  ### 硬盘信息
  - 磁盘 xx: PHIXERO  （1907.73GB）
  - 磁盘 xx: XPG GAMMIX S70 BLADE （953.86GB）
  - 总容量：2861.59GB

- 实现一键复制功能，复制内容使用中文标签格式化。
- 显卡信息过滤，仅显示PCI显卡。
- 硬件信息加载采用异步逻辑。

## 5. 优化清理视图

- 配置文件：
  - PowerShell 命令：`config/powershell_commands.py`
  - 注册表命令：`config/registry_commands.py`
  - 系统清理：`config/system_cleanup.py`

- 采用选项卡布局，包含 PowerShell 设置、注册表优化、系统清理。
- 复选框支持父子联动逻辑。
- 全选复选框支持全选/取消全选所有选项卡的选项。
- 提供执行选中任务按钮和一键执行所有任务按钮。
- 执行后自动重启文件资源管理器并删除图标缓存。

## 6. 预装卸载视图

- 配置文件：
  - 预装应用：`config/appx_packages.py`
  - OneDrive 清理：`config/onedrive_cleanup.py`

- 选项卡布局，按分类展示应用（Xbox、商店应用、音视频编解码器、系统应用、OneDrive清理）。
- 全选复选框支持全选/取消全选所有选项卡的选项。
- 卸载选中应用按钮和一键卸载所有应用按钮。
- 按需查找机制，点击执行时逐个查找应用包并卸载，避免一次性扫描。

## 7. 超频工具视图

- OCTools 文件夹扫描逻辑，若不存在则创建。
- 可执行文件检测和启动，优先查找同名.exe，否则任意.exe。
- GUI/控制台程序自动识别，采用合适方式启动。
- 子进程启动，确保以管理员权限运行。
- 统一尺寸工具卡片展示，显示工具名称和图标。
- 点击卡片直接启动工具。
- 图标提取和显示，参考提供的代码逻辑。
 ```python 
 class ExecutableIconExtractor: 
     def __init__(self): 
         self._icon_provider = QIcon() 
 
     def get_best_icon(self, file_path: str, requested_size: QSize = QSize(64, 64)) -> QPixmap: 
         # 验证文件路径 
         if not self._is_valid_exe(file_path): 
             return QPixmap() 
 
         # 获取文件图标 
         file_info = QFileInfo(file_path) 
         icon = self._get_file_icon(file_info) 
         
         # 选择最佳尺寸 
         return self._select_best_icon(icon, requested_size) 
 
     def _is_valid_exe(self, file_path: str) -> bool: 
         if not os.path.exists(file_path): 
             return False 
         if not file_path.lower().endswith('.exe'): 
             return False 
         return True 
 
     def _get_file_icon(self, file_info: QFileInfo) -> QIcon: 
         return QIcon.fromTheme(file_info.completeBaseName()) or QIcon(file_info.filePath()) 
 
     def _select_best_icon(self, icon: QIcon, requested_size: QSize) -> QPixmap: 
         if icon.isNull(): 
             return QPixmap() 
 
         available_sizes = icon.availableSizes() 
         
         # 查找完全匹配的尺寸 
         exact_match = next((s for s in available_sizes if s == requested_size), None) 
         if exact_match: 
             return icon.pixmap(exact_match) 
             
         # 查找不超过请求尺寸的最大尺寸 
         valid_sizes = [s for s in available_sizes 
                       if s.width() <= requested_size.width() 
                       and s.height() <= requested_size.height()] 
         
         if valid_sizes: 
             # 按面积排序选择最大尺寸 
             best_size = max(valid_sizes, key=lambda s: s.width() * s.height()) 
             return icon.pixmap(best_size) 
             
         # 默认返回请求尺寸 
         return icon.pixmap(requested_size) 
 ```
- 重启进入BIOS时提供二次确认对话框。

## 8. 快捷工具视图

- 配置文件：`config/quick_tools.py`
- 子进程启动，确保以管理员权限运行。
- 按分类展示工具，按钮展示，点击按钮直接运行。
- 高风险操作提供二次确认对话框（安全模式、重启、关机、睡眠、锁定）。
- 特别处理安全模式为一次性操作，重启后恢复正常模式。

## 9. 设置关于视图

- 应用信息展示（图标、名称、版本、版权）。
- 检查更新：点击跳转外部链接。
- 赞助作者：弹出对话框，包含支付宝和微信二维码两个选项卡。
- 抖音主页：点击跳转作者抖音主页。
- 官方QQ群：点击跳转QQ群。