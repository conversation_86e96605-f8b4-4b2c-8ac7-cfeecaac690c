#!/usr/bin/env python3
# -*- coding: utf-8 -*-

POWERSHELL_COMMANDS = {
    "执行策略设置": {
        "设置执行策略为Bypass": "Set-ExecutionPolicy Bypass -Scope LocalMachine -Force"
    },
    "电源管理": {
        "解锁所有电源选项": "Get-ChildItem 'HKLM:\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings' -Recurse | Where-Object {$_.PSPath -notmatch '\\\\DefaultPowerSchemeValues|(\\\\[0-9]|\\\\255)$'} | ForEach-Object {Set-ItemProperty -Path $_.PSPath -Name 'Attributes' -Value 2 -Force}"
    }
}
